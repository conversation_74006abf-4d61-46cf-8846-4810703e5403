# SW AI Federated Modules

A collection of AI-powered federated modules designed for DingTalk OA integration, built with React 16, TypeScript, and module federation using RSBuild.

## 🚀 Features

- **Type-Safe Form Data Handling**: Comprehensive TypeScript types for all form field types
- **Enhanced Context API**: Extended render context with powerful form data manipulation methods
- **Utility Functions**: Rich set of utilities for filtering, searching, exporting, and analyzing form data
- **AI Form Fill Component**: Interactive component for managing and visualizing form data
- **Module Federation**: Seamlessly integrates with existing DingTalk OA applications

## 🏗️ Technology Stack

- **React 16** - UI framework
- **TypeScript** - Type safety and better developer experience
- **RSBuild** - Build tool with module federation support
- **PNPM** - Package manager
- **CSS3** - Modern styling with animations and responsive design

## 📁 Project Structure

```
src/
├── components/           # Shared components
│   ├── ai-form-fill.tsx/ # AI form fill component with styles
│   ├── form-data-demo.tsx # Comprehensive demo component
│   └── form-data-demo.css # Demo styling
├── entries/             # Federated module entry points
│   └── ai-form-fill.tsx # Main entry for AI form fill module
├── types/               # TypeScript type definitions
│   ├── form-data.type.ts      # Form data types based on sample analysis
│   ├── render-context.type.ts # Enhanced context API types
│   └── schema.type.ts         # Form schema types
├── utils/               # Utility functions
│   └── index.ts         # Form data manipulation utilities
└── mock/                # Sample data
    ├── sample.schema.json     # Sample form schema
    └── sample.formValue.mdx   # Sample form data values
```

## 🎯 Core Types

### Form Data Types

Based on actual DingTalk form data analysis, we provide specific types for each field type:

```typescript
// Base form field structure
interface IFormFieldData {
  key: string;
  label?: string;
  value: any;
  extendValue?: any;
}

// Specific field types
interface ITextFieldData extends IFormFieldData {
  value: string;
}

interface ISelectFieldData extends IFormFieldData {
  value: string;
  extendValue: ISelectOption;
}

interface IMultiSelectFieldData extends IFormFieldData {
  value: string[];
  extendValue: ISelectOption[];
}

// And many more specific types...
```

### Enhanced Context API

The `Context` class has been enhanced with powerful methods for form data manipulation:

```typescript
class Context {
  // Get form data (matches window.__ctx.getFormData())
  getFormData(): Promise<IFormDataArray>;
  
  // Get form statistics
  getFormStats(): IFormDataStats;
  
  // Set field values
  setFieldValue(fieldKey: string, value: any): Promise<boolean>;
  
  // Validate form
  validateForm(): Promise<IFormValidationResult>;
  
  // Export form data
  exportFormData(format: 'json' | 'csv' | 'xml'): Promise<string>;
  
  // Search and filter
  searchFields(searchTerm: string): IFormFieldDataUnion[];
  getFieldsByType(componentType: string): IFormFieldDataUnion[];
  
  // And many more utility methods...
}
```

## 🛠️ Utility Functions

Rich set of utility functions for form data manipulation:

```typescript
// Data transformation
formDataArrayToMap(formData: IFormDataArray): IFormDataMap;
getFlatFormData(formData: IFormDataArray): Record<string, any>;

// Filtering
filterFieldsWithValues(formData: IFormDataArray): IFormDataArray;
filterEmptyFields(formData: IFormDataArray): IFormDataArray;
searchFields(formData: IFormDataArray, searchTerm: string): IFormDataArray;

// Analysis
calculateFormStats(formData: IFormDataArray, schema: any[]): IFormDataStats;
isFormComplete(formData: IFormDataArray, schema: any[]): boolean;

// Export
exportToJSON(formData: IFormDataArray, options?: IFormDataExportOptions): string;
exportToCSV(formData: IFormDataArray, options?: IFormDataExportOptions): string;

// Comparison
compareFormData(formData1: IFormDataArray, formData2: IFormDataArray): FormDataDiff;
```

## 📦 Installation

```bash
# Install dependencies
pnpm install

# Development server
pnpm dev

# Build for production
pnpm build

# Type checking
pnpm type-check
```

## 🎨 Components

### AIFormFill Component

Interactive component for managing form data with features like:

- Real-time form data display
- Field search and filtering
- Form validation
- Data export functionality
- Debug mode with detailed logging

```tsx
import { AIFormFill } from './components/ai-form-fill.tsx/ai-form-fill';

<AIFormFill 
  context={window.__ctx} 
  debug={true} 
/>
```

### FormDataDemo Component

Comprehensive demo showcasing all the enhanced types and utilities:

```tsx
import { FormDataDemo } from './components/form-data-demo';

<FormDataDemo />
```

## 🔍 Usage Examples

### Type-Safe Field Access

```typescript
// Get form data with proper typing
const formData = await context.getFormData();

// Type-safe field access
const textField = formData.find(f => 
  f.key === 'TextField-K2AD4O5B'
) as ITextFieldData;

// TypeScript knows this is a string
console.log(textField.value.toUpperCase());

const selectField = formData.find(f => 
  f.key === 'DDSelectField_1Y7FJU91S6L1C'
) as ISelectFieldData;

// Access extended value with type safety
console.log(selectField.extendValue.label);
```

### Form Data Analysis

```typescript
// Get form statistics
const stats = context.getFormStats();
console.log(`Completion: ${stats.completionRate}%`);

// Check if form is complete
const isComplete = isFormComplete(formData, schema);

// Get empty fields that need attention
const emptyFields = filterEmptyFields(formData);

// Search for specific fields
const dateFields = searchFields(formData, '日期');
```

### Data Export

```typescript
// Export to JSON with options
const jsonData = exportToJSON(formData, {
  includeExtendValue: true,
  includeEmpty: false,
  excludeFields: ['internal_field']
});

// Export to CSV
const csvData = exportToCSV(formData);

// Create downloadable file
const blob = new Blob([jsonData], { type: 'application/json' });
const url = URL.createObjectURL(blob);
```

## 🔧 Integration with DingTalk OA

This module is designed to work seamlessly with DingTalk OA approval forms:

1. **Context Integration**: Automatically detects and uses `window.__ctx` when available
2. **Data Compatibility**: Types match actual DingTalk form data structure
3. **Field Types**: Supports all DingTalk form field types (text, select, date, etc.)
4. **Employee Data**: Handles DingTalk employee and department information

## 🎯 Sample Data Structure

Based on actual DingTalk form data (`window.__ctx.getFormData()` result):

```javascript
[
  {
    key: "dingtalk_origin_dept",
    value: "010608436821883588",
    extendValue: {
      name: "叶青楠",
      emplId: "010608436821883588",
      avatar: "https://static.dingtalk.com/media/..."
    }
  },
  {
    key: "TextField-K2AD4O5B",
    label: "单行输入框",
    value: "你好，Arno"
  },
  {
    key: "DDSelectField_1Y7FJU91S6L1C",
    label: "单选框",
    value: "选项1",
    extendValue: { label: "选项1", key: "option_0" }
  }
  // ... more fields
]
```

## 🚀 Module Federation

This project supports module federation for seamless integration:

```javascript
// In your host application
const AIFormFillModule = await import('sw-ai-federated-modules/ai-form-fill');
const AIFormFill = AIFormFillModule.default;

// Use the component
<AIFormFill context={window.__ctx} />
```

## 📝 Development

### Adding New Field Types

1. Define the interface in `types/form-data.type.ts`
2. Add to the union type `IFormFieldDataUnion`
3. Add utility functions in `utils/index.ts`
4. Update the demo component

### Testing

The project includes comprehensive type checking and sample data for testing:

```bash
# Run type checking
pnpm type-check

# Test with sample data
# Open browser console and use the demo buttons
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with proper TypeScript types
4. Test with the demo component
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

---

Built with ❤️ for DingTalk OA integration using modern TypeScript and React patterns.
