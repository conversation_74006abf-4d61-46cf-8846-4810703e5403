import { jsonrepair } from 'jsonrepair';

import { LLMConfig, LLMResponse, Message, streamType } from './llm-service.type';

export class ManusError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ManusError';
  }
}

export class ToolExecutionError extends ManusError {
  constructor(toolName: string, message: string) {
    super(`Tool '${toolName}' execution failed: ${message}`);
    this.name = 'ToolExecutionError';
  }
}

export class LLMError extends ManusError {
  constructor(message: string) {
    super(`LLM error: ${message}`);
    this.name = 'LLMError';
  }
}

const ENABLE_LLM_SERVICE_MSG_LOG = location.search.includes('__ENABLE_LLM_SERVICE_MSG_LOG=true');

export interface IPromptBuilderContext {
  enableRadixUI: boolean;
  enableAntd: boolean;
  scenarioContext: string;
  isDynamicDataSource: boolean;
  isModifyCode: boolean;
  appContext?: string;
  pageContext?: PageContext;
  enableYidaComponents: boolean;
  scenario?: {
    normal?: boolean;
    portal?: boolean;
    application?: boolean;
  };
}

interface PageContext {
  basicInfo: string;
  cubeInfo: string;
  dataSourceAPIInfo: string;
}

export class LLMService {
  private abortController: AbortController | null = null;
  private provider: Record<string, any> | null = null;

  protected model: string;
  // LLM的配置
  protected config: LLMConfig;

  constructor(config?: Partial<LLMConfig>) {
    // 定义用哪个模型作为默认模型
    // this.model = config.model || 'qwen-max';
    this.model = config?.model || 'dingtalk-qwen3';

    this.config = {
      model: this.model,
      temperature: 0.7,
      maxTokens: 8000,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      ...config,
    };

    if (this.provider) {
      this.config.provider = this.provider;
    }
  }


  static getUrl(): string {
    if (
      process.env.NODE_ENV === 'development' &&
      new URL(location.href).searchParams.get('ENABLE_LOCAL_SERVER') === 'true'
    ) {
      return `http://localhost:3000/APP_UO4Q7OWSBHEMD3R26Z4J/doc2bot/action`;
    } else {
      return `https://dd-partner-sse.pre-fc.alibaba-inc.com/APP_UO4Q7OWSBHEMD3R26Z4J/doc2bot/action`;
    }
  }

  // 浏览器环境中的流式响应实现
  async askStream<Context = any>(
    messages: Message[],
    systemMessages?: Message[],
    tools?: Array<{ type: string; function: { name: string; description: string; parameters?: any } }>,
    toolChoice: 'none' | 'auto' | 'required' = 'auto',
    onStream?: (type: streamType, data: any, abort?: () => void) => void,
    jsonFormat?: boolean,
    options?: {
      context?: Context;
      enableThinking?: boolean;
    },
  ): Promise<LLMResponse> {
    try {
      this.abortController = new AbortController();
      const chatMessages = [...(systemMessages || []), ...(messages || [])];
      const convertedChatMessages = this.convertMessagesToLLMFormat(chatMessages, {
        model: this.model,
      });
      // 使用 fetch API 代替 axios 来处理流式响应
      // eslint-disable-next-line @iceworks/best-practices/recommend-polyfill
      // const response = await fetch(`${this.baseUrl}/chat/completions`, {
      // const response = await fetch(`http://localhost:3000/${g_config.appKey}/doc2bot/action`, {
      const response = await fetch(LLMService.getUrl(), {
        method: 'POST',
        headers: {
          Accept: 'text/event-stream',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cfg: this.config,
          messages: convertedChatMessages,
          tools: tools?.length ? tools : undefined,
          tool_choice: toolChoice,
          stream: true,
          response_format: {
            type: jsonFormat ? 'json_object' : 'text',
          },
          // speed first ranking when enable-openrouter for local dev
          provider: {
            sort: 'throughput',
          },
          enableThinking: options?.enableThinking || false,
          enable_thinking: options?.enableThinking || false,
          /**
           * LLM 系统提示词的上下文变量、控制开关
           */
          context: (options?.context || {}) as IPromptBuilderContext,
        }),
        signal: this.abortController.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || `HTTP error ${response.status}`);
      }

      if (!response.body) {
        throw new Error('Response body is null');
      }

      const reader = response.body.getReader();
      // eslint-disable-next-line @iceworks/best-practices/recommend-polyfill
      const decoder = new TextDecoder('utf-8');
      let traceId;
      let reasoningContent = '';
      let thinkingContent = '';
      const toolInfo: any[] = [];

      let interruptFrame: string | null = null;
      // 处理流式响应
      // eslint-disable-next-line no-constant-condition
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n').filter((line) => {
          const tr = line.trim();
          return tr !== '' && tr !== 'event:message';
        });
        let traceId = '';
        // console.log('Delta content: >>>>>>>>>>>>', JSON.stringify(lines));
        for (let line of lines) {
          let res = /([a-zA-Z]+):(.*)/.exec(line);
          if ((!res || res.length < 3) && interruptFrame) {
            line = interruptFrame + line;
            res = /([a-zA-Z]+):(.*)/.exec(line);
          }
          if (res && res.length > 2) {
            const [, protocol, content] = res;
            if (protocol === 'traceId') {
              traceId = content.trim();
              interruptFrame = null;
            }
            if (protocol === 'data') {
              const data = content.trim();
              if (data.length > 0) {
                if (data === '[DONE]') continue;
                try {
                  const parsed = JSON.parse(data);
                  const delta = parsed.choices[0]?.delta || {};
                  if (!delta) {
                    continue;
                  }
                  // 添加调试日志
                  if (delta.reasoning_content) {
                    reasoningContent += delta.reasoning_content;
                    if (onStream) {
                      onStream(streamType.REASONING, reasoningContent, () => {
                        this.interrupt();
                      });
                    }
                  } else {
                    // 处理回复内容
                    if (delta.content) {
                      // console.log('Delta content: >>>>>>>>>>>>', delta.content);
                      thinkingContent += delta.content;
                      // 如果设置了回调函数，则调用
                      if (onStream) {
                        onStream(streamType.THINKING, thinkingContent, () => {
                          this.interrupt();
                        });
                      }
                    }
                    // 处理工具调用
                    if (delta.tool_calls && delta.tool_calls.length > 0) {
                      for (const toolCall of delta.tool_calls) {
                        const { index } = toolCall;
                        // 确保数组长度足够
                        while (toolInfo.length <= index) {
                          toolInfo.push({});
                        }
                        // 更新工具ID
                        if (toolCall.id) {
                          toolInfo[index].id = (toolInfo[index].id || '') + toolCall.id;
                        }
                        // 更新函数名称
                        if (toolCall.function?.name) {
                          toolInfo[index].name = (toolInfo[index].name || '') + toolCall.function.name;
                        }
                        // 更新参数 - 确保累加参数
                        if (toolCall.function?.arguments) {
                          const currentArguments = (toolInfo[index].arguments || '') + toolCall.function.arguments;
                          toolInfo[index].arguments = currentArguments;
                          const repairedJson = jsonrepair(currentArguments)
                            .replace(/\n/g, '')
                            .replace(/(,(\s)*)?"":null/g, '');
                          if (onStream) {
                            onStream(streamType.TOOL_CALL, {
                              tool: toolInfo[index].name,
                              args: repairedJson,
                            });
                          }
                        }
                      }
                    }
                  }
                  interruptFrame = null;
                  continue;
                } catch (e) {
                  // eslint-disable-next-line no-console
                  console.error('Error parsing stream data:', e);
                }
                interruptFrame = line;
              } else {
                interruptFrame = line;
                continue;
              }
            }
          } else {
            interruptFrame = line;
          }
        }
      }
      const newToolCalls = toolInfo.map((tool) => ({
        id: tool.id,
        type: 'function',
        function: {
          name: tool.name,
          arguments: tool.arguments,
        },
      }));
      const result = {
        content: thinkingContent,
        thinking: reasoningContent,
        toolCalls: newToolCalls.length > 0 ? newToolCalls : undefined,
        traceId,
      };
      // 添加调试日志
      console.log('[LLMService] 最终返回结果: ', {
        contentLength: result.content?.length || 0,
        thinkingLength: result.thinking?.length || 0,
        hasToolCalls: !!result.toolCalls,
        toolCallsCount: result.toolCalls?.length || 0,
      });

      // console.log(' Call function: ', traceId, tools, toolInfo);
      return result;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new LLMError('请求被中断');
      }
      throw new LLMError(`Unexpected stream error: ${error}`);
    } finally {
      this.abortController = null;
    }
  }

  // 更新配置
  updateConfig(config: Partial<LLMConfig>): void {
    this.config = { ...this.config, ...config };
  }

  // 获取当前配置
  getConfig(): LLMConfig {
    return { ...this.config };
  }

  interrupt(): void {
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }
  }

  /**
   * 将代理内部消息格式转换为LLM所需的格式
   * @param messages 代理内部消息格式
   * @returns 转换后的LLM所需消息格式
   */
  private convertMessagesToLLMFormat(
    messages: Message[],
    options?: {
      model?: string;
      filterUserMsg?: (msg: Message) => boolean;
    },
  ): any[] {
    const transformedMsgList = messages
      .map((msg) => {
        if (msg.role === 'system') {
          return msg;
        }
        if (msg.role === 'user') {
          return msg;
        }
        if (msg.role === 'assistant') {
          const result: any = {
            role: 'assistant',
            content: msg.content,
          };
          if (msg.toolCalls && msg.toolCalls.length > 0) {
            result.thought = msg.content;
            const firstToolCall = msg.toolCalls[0];
            result.tool_calls = [
              {
                type: 'function',
                function: {
                  name: firstToolCall.function.name,
                  arguments: firstToolCall.function.arguments,
                },
                id: firstToolCall.id,
              },
            ];
          }
          return result;
        }

        if (msg.role === 'tool') {
          const result: any = {
            role: 'tool',
            name: msg.name,
            content: msg.content,
          };
          result.tool_call_id = msg.toolCallId;
          return result;
        }
        return msg;
      })
      .filter(Boolean);

    if (ENABLE_LLM_SERVICE_MSG_LOG) {
      console.info('💬 [LLMService] messages: ', transformedMsgList);
    }

    return transformedMsgList;
  }
}
