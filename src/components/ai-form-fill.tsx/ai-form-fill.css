/* AI Form Fill Component Styles */
.ai-form-fill {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ai-form-fill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ai-form-fill-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.form-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.form-stats span {
  padding: 0.25rem 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.ai-form-fill-controls {
  display: flex;
  gap: 0.5rem;
  padding: 1rem;
  background: white;
  border-bottom: 1px solid #e0e0e0;
}

.search-input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.search-input:focus {
  outline: none;
  border-color: #0066cc;
  box-shadow: 0 0 0 2px rgba(0,102,204,0.2);
}

.validate-btn,
.export-btn,
.refresh-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.validate-btn:hover,
.export-btn:hover,
.refresh-btn:hover {
  background: #f8f9fa;
  border-color: #0066cc;
}

.validate-btn {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.validate-btn:hover {
  background: #218838;
}

.export-btn {
  background: #17a2b8;
  color: white;
  border-color: #17a2b8;
}

.export-btn:hover {
  background: #138496;
}

.ai-form-fill-content {
  display: flex;
  flex: 1;
  gap: 1rem;
  padding: 1rem;
}

.field-list {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow-y: auto;
}

.field-list h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.field-item {
  padding: 0.75rem;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.field-item:hover {
  border-color: #0066cc;
  box-shadow: 0 2px 8px rgba(0,102,204,0.1);
}

.field-item.selected {
  border-color: #0066cc;
  background: #f0f8ff;
  box-shadow: 0 2px 8px rgba(0,102,204,0.2);
}

.field-item.filled {
  border-left: 4px solid #28a745;
}

.field-item.empty {
  border-left: 4px solid #ffc107;
}

.field-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.filled-indicator {
  color: #28a745;
  font-size: 0.8rem;
}

.field-value {
  font-size: 0.9rem;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.field-details {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow-y: auto;
}

.field-details-content h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e0e0e0;
}

.detail-section {
  margin-bottom: 1rem;
}

.detail-section label {
  display: block;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.detail-section code {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.85rem;
  color: #495057;
}

.detail-section pre {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 0.75rem;
  margin: 0;
  overflow-x: auto;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.8rem;
  color: #495057;
  line-height: 1.4;
}

.debug-panel {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.debug-panel h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.debug-tabs details {
  margin-bottom: 0.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.debug-tabs summary {
  padding: 0.5rem;
  background: #f8f9fa;
  cursor: pointer;
  font-weight: 500;
  border-radius: 4px 4px 0 0;
}

.debug-tabs summary:hover {
  background: #e9ecef;
}

.debug-tabs pre {
  margin: 0;
  padding: 0.75rem;
  background: #f8f9fa;
  border-top: 1px solid #e0e0e0;
  overflow-x: auto;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.75rem;
  line-height: 1.4;
}

.ai-form-fill-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.1rem;
  color: #666;
}

.ai-form-fill-error {
  padding: 1rem;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  color: #721c24;
  margin: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .ai-form-fill-content {
    flex-direction: column;
  }
  
  .field-details {
    max-height: 300px;
  }
  
  .ai-form-fill-controls {
    flex-wrap: wrap;
  }
  
  .search-input {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}

/* Animation for loading states */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.field-item.loading {
  animation: pulse 1.5s infinite;
}
