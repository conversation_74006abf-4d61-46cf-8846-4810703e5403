import React, { useState, useEffect, useCallback } from 'react';

export const AIFormFill: React.FC = () => {
  return <div>AIFormFill</div>;
};

// import './ai-form-fill.css';
// import { IFormDataArray, IFormFieldDataUnion, IFormDataStats } from '../../types/form-data.type';
// import { Context } from '../../types/render-context.type';
// import {
//   getFlatFormData,
//   filterFieldsWithValues,
//   filterEmptyFields,
//   searchFields,
//   exportToJSON,
//   generateFieldSummary,
// } from '../../utils';
// import { LLMService } from '@/libs/llm-service';
// import { streamType } from '@/libs/llm-service.type';

// interface AIFormFillProps {
//   /** Form context instance */
//   context?: Context;
//   /** Whether to show debug information */
//   debug?: boolean;
// }

// export const AIFormFill: React.FC<AIFormFillProps> = ({ context, debug = false }) => {
//   const [formData, setFormData] = useState<IFormDataArray>([]);
//   const [formStats, setFormStats] = useState<IFormDataStats | null>(null);
//   const [selectedField, setSelectedField] = useState<string>('');
//   const [searchTerm, setSearchTerm] = useState<string>('');
//   const [loading, setLoading] = useState<boolean>(false);
//   const [error, setError] = useState<string>('');

//   // Load form data when context is available
//   useEffect(() => {
//     if (context) {
//       loadFormData();
//     }
//   }, [context]);

//   useEffect(() => {
//     const asyncFn = async () => {
//       const llmService = new LLMService();
//       llmService.askStream(
//         [{
//           role: 'user',
//           content: '你好，世界！'
//         }],
//         [],
//         [],
//         'auto',
//         (type, data) => {
//           console.log('type', type);
//           if (type === streamType.THINKING) {
//             console.log('text', data);
//           } else if (type === streamType.REASONING) {
//             console.log('reasoning', data);
//           }
//         },
//         false,
//         {}
//       );
//     };
//     asyncFn();
//   }, []);

//   const loadFormData = useCallback(async () => {
//     if (!context) return;

//     try {
//       setLoading(true);
//       setError('');

//       // Get form data using the enhanced context methods
//       const data = await context.getFormData();
//       setFormData(data);

//       // Calculate form statistics
//       const stats = context.getFormStats();
//       setFormStats(stats);

//       if (debug) {
//         console.log('Form Data:', data);
//         console.log('Form Stats:', stats);
//         console.log('Form Summary:', generateFieldSummary(data));
//       }
//     } catch (err) {
//       setError(`Failed to load form data: ${err}`);
//       console.error('Error loading form data:', err);
//     } finally {
//       setLoading(false);
//     }
//   }, [context, debug]);

//   const handleValidateForm = useCallback(async () => {
//     if (!context) return;

//     try {
//       const validationResult = await context.validateForm();
//       if (validationResult.isValid) {
//         alert('Form is valid!');
//       } else {
//         alert(`Form has errors in fields: ${validationResult.invalidFields.join(', ')}`);
//       }
//     } catch (err) {
//       setError(`Validation failed: ${err}`);
//     }
//   }, [context]);

//   const handleExportData = useCallback(() => {
//     if (formData.length === 0) return;

//     const jsonData = exportToJSON(formData, {
//       includeExtendValue: true,
//       includeEmpty: false,
//     });

//     // Create download link
//     const blob = new Blob([jsonData], { type: 'application/json' });
//     const url = URL.createObjectURL(blob);
//     const a = document.createElement('a');
//     a.href = url;
//     a.download = 'form-data.json';
//     document.body.appendChild(a);
//     a.click();
//     document.body.removeChild(a);
//     URL.revokeObjectURL(url);
//   }, [formData]);

//   const filteredFields = searchTerm ? searchFields(formData, searchTerm) : formData;

//   const filledFields = filterFieldsWithValues(formData);
//   const emptyFields = filterEmptyFields(formData);

//   if (loading) {
//     return <div className="ai-form-fill-loading">Loading form data...</div>;
//   }

//   if (error) {
//     return <div className="ai-form-fill-error">Error: {error}</div>;
//   }

//   return (
//     <div className="ai-form-fill">
//       <div className="ai-form-fill-header">
//         <h2>AI Form Fill Assistant</h2>
//         {formStats && (
//           <div className="form-stats">
//             <span>Total: {formStats.totalFields}</span>
//             <span>Filled: {formStats.filledFields}</span>
//             <span>Completion: {formStats.completionRate.toFixed(1)}%</span>
//           </div>
//         )}
//       </div>

//       <div className="ai-form-fill-controls">
//         <input
//           type="text"
//           placeholder="Search fields..."
//           value={searchTerm}
//           onChange={(e) => setSearchTerm(e.target.value)}
//           className="search-input"
//         />
//         <button onClick={handleValidateForm} className="validate-btn">
//           Validate Form
//         </button>
//         <button onClick={handleExportData} className="export-btn">
//           Export Data
//         </button>
//         <button onClick={loadFormData} className="refresh-btn">
//           Refresh
//         </button>
//       </div>

//       <div className="ai-form-fill-content">
//         <div className="field-list">
//           <h3>Form Fields ({filteredFields.length})</h3>
//           {filteredFields.map((field) => (
//             <FieldItem
//               key={field.key}
//               field={field}
//               isSelected={selectedField === field.key}
//               onSelect={() => setSelectedField(field.key)}
//             />
//           ))}
//         </div>

//         {selectedField && (
//           <div className="field-details">
//             <FieldDetails field={formData.find((f) => f.key === selectedField)!} />
//           </div>
//         )}
//       </div>

//       {debug && (
//         <div className="debug-panel">
//           <h3>Debug Information</h3>
//           <div className="debug-tabs">
//             <details>
//               <summary>Filled Fields ({filledFields.length})</summary>
//               <pre>{JSON.stringify(filledFields, null, 2)}</pre>
//             </details>
//             <details>
//               <summary>Empty Fields ({emptyFields.length})</summary>
//               <pre>{JSON.stringify(emptyFields, null, 2)}</pre>
//             </details>
//             <details>
//               <summary>All Form Data</summary>
//               <pre>{JSON.stringify(formData, null, 2)}</pre>
//             </details>
//             <details>
//               <summary>Flat Data</summary>
//               <pre>{JSON.stringify(getFlatFormData(formData), null, 2)}</pre>
//             </details>
//           </div>
//         </div>
//       )}
//     </div>
//   );
// };

// // Helper component for individual field items
// const FieldItem: React.FC<{
//   field: IFormFieldDataUnion;
//   isSelected: boolean;
//   onSelect: () => void;
// }> = ({ field, isSelected, onSelect }) => {
//   const hasValue = field.value !== undefined && field.value !== null && field.value !== '';

//   return (
//     <div
//       className={`field-item ${isSelected ? 'selected' : ''} ${hasValue ? 'filled' : 'empty'}`}
//       onClick={onSelect}
//     >
//       <div className="field-label">
//         {field.label || field.key}
//         {hasValue && <span className="filled-indicator">●</span>}
//       </div>
//       <div className="field-value">
//         {Array.isArray(field.value)
//           ? `[${field.value.length} items]`
//           : String(field.value || '(empty)')}
//       </div>
//     </div>
//   );
// };

// // Helper component for field details
// const FieldDetails: React.FC<{
//   field: IFormFieldDataUnion;
// }> = ({ field }) => {
//   return (
//     <div className="field-details-content">
//       <h4>{field.label || field.key}</h4>
//       <div className="detail-section">
//         <label>Key:</label>
//         <code>{field.key}</code>
//       </div>
//       <div className="detail-section">
//         <label>Value:</label>
//         <pre>{JSON.stringify(field.value, null, 2)}</pre>
//       </div>
//       {field.extendValue && (
//         <div className="detail-section">
//           <label>Extended Value:</label>
//           <pre>{JSON.stringify(field.extendValue, null, 2)}</pre>
//         </div>
//       )}
//     </div>
//   );
// };
