/* Form Data Demo Styles */
.form-data-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f8f9fa;
  min-height: 100vh;
}

.form-data-demo h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.demo-controls {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.search-section {
  margin-bottom: 1rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
}

.demo-buttons {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.demo-buttons button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.demo-buttons button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102,126,234,0.3);
}

.demo-buttons button:active {
  transform: translateY(0);
}

.stats-panel {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.stats-panel h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.stat-item label {
  font-weight: 500;
  color: #495057;
}

.stat-item span {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1.1rem;
}

.demo-tabs {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  overflow: hidden;
  margin-bottom: 2rem;
}

.tab-buttons {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.tab-buttons button {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  color: #495057;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-buttons button:hover {
  background: #e9ecef;
  color: #2c3e50;
}

.tab-buttons button.active {
  background: white;
  color: #667eea;
  border-bottom-color: #667eea;
}

.tab-content {
  padding: 2rem;
}

.overview-panel h3,
.fields-panel h3,
.types-panel h3 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.4rem;
}

.overview-panel ul {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #28a745;
  margin: 1rem 0;
}

.overview-panel li {
  margin: 0.5rem 0;
}

.quick-stats {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.quick-stats p {
  margin: 0.5rem 0;
  font-size: 1.1rem;
}

.fields-list {
  display: grid;
  gap: 1rem;
}

.field-card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.field-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102,126,234,0.1);
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e9ecef;
}

.field-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.field-type {
  background: #667eea;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.field-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-value,
.field-extend {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.field-value strong,
.field-extend strong {
  color: #495057;
  font-size: 0.9rem;
}

.field-value code,
.field-extend code {
  background: white;
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.85rem;
  color: #495057;
  word-break: break-all;
}

.code-examples {
  display: grid;
  gap: 1.5rem;
}

.code-block {
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.code-block h4 {
  margin: 0;
  padding: 1rem;
  background: #667eea;
  color: white;
  font-size: 1rem;
}

.code-block pre {
  margin: 0;
  padding: 1.5rem;
  background: #2d3748;
  color: #e2e8f0;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  overflow-x: auto;
}

.footer {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  margin-top: 2rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.footer p {
  margin: 0.5rem 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .form-data-demo {
    padding: 1rem;
  }
  
  .form-data-demo h1 {
    font-size: 2rem;
  }
  
  .demo-buttons {
    justify-content: center;
  }
  
  .demo-buttons button {
    flex: 1;
    min-width: 120px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .tab-buttons {
    flex-direction: column;
  }
  
  .tab-content {
    padding: 1rem;
  }
  
  .field-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Animation effects */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.field-card {
  animation: fadeIn 0.5s ease-out;
}

.stat-item {
  animation: fadeIn 0.5s ease-out;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #667eea;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
