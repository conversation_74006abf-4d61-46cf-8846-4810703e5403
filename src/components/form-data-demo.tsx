/**
 * @fileoverview Comprehensive demo showing how to use the enhanced form data types and utilities
 * This file demonstrates real-world usage patterns for the SW AI Federated Modules
 */

import React, { useState, useEffect } from 'react';
import './form-data-demo.css';
import {
  IFormDataArray,
  IFormDataStats,
  IFormDataExportOptions,
  ISelectFieldData,
  ITextFieldData,
  INumberFieldData,
  IDateFieldData
} from '../types/form-data.type';
import {
  formDataArrayToMap,
  getFlatFormData,
  filterFieldsWithValues,
  filterEmptyFields,
  searchFields,
  exportToJSON,
  exportToCSV,
  calculateFormStats,
  isFormComplete,
  compareFormData,
  generateFieldSummary
} from '../utils';
import sampleSchema from '../mock/sample.schema.json';

// Mock form data based on the sample provided
const mockFormData: IFormDataArray = [
  {
    key: "dingtalk_origin_dept",
    label: undefined,
    value: "010608436821883588",
    extendValue: {
      name: "叶青楠",
      emplId: "010608436821883588",
      avatar: "https://static.dingtalk.com/media/lQDPD4XIA7nXAp_NA-jNA-iwbU2EXAnbEhIHxd2A7FCeAA_1000_1000.jpg"
    }
  },
  {
    key: "TextField-K2AD4O5B",
    label: "单行输入框",
    value: "你好，Arno"
  } as ITextFieldData,
  {
    key: "TextareaField_110SFYLNA8PA8",
    label: "多行输入框",
    value: "你好，世界"
  } as ITextFieldData,
  {
    key: "NumberField_1D5JDEQLZM680",
    label: "数字输入框",
    value: "12"
  } as INumberFieldData,
  {
    key: "DDSelectField_1Y7FJU91S6L1C",
    label: "单选框",
    value: "选项1",
    extendValue: { label: "选项1", key: "option_0" }
  } as ISelectFieldData,
  {
    key: "DDMultiSelectField_1UE23J2DT3OJK",
    label: "多选框",
    value: ["选项2", "选项3"],
    extendValue: [{ key: "option_1" }, { key: "option_2" }]
  },
  {
    key: "DDDateField_1DNGPEEYEZPXC",
    label: "日期",
    value: "2025-06-05 04:00"
  } as IDateFieldData,
  {
    key: "DDDateField_1DT863BBVSUF4",
    label: "日期（年月日）",
    value: "2025-06-06"
  } as IDateFieldData,
  {
    key: "MoneyField_16RI5M0HL3Y0W",
    label: "金额（元）",
    value: "22"
  },
  {
    key: "AddressField_17VDOA9OVFZ4",
    label: "省市区（街道）",
    value: "杭州市"
  },
  {
    key: "AddressField_10MGAME3F1KAO",
    label: "省市区",
    value: "四川省"
  },
  {
    key: "AddressField_G6PDNS571QM8",
    label: "省市",
    value: "内江市"
  }
];

export const FormDataDemo: React.FC = () => {
  const [currentFormData, setCurrentFormData] = useState<IFormDataArray>(mockFormData);
  const [originalFormData] = useState<IFormDataArray>(mockFormData);
  const [stats, setStats] = useState<IFormDataStats | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDemo, setSelectedDemo] = useState<string>('overview');

  useEffect(() => {
    // Calculate stats when form data changes
    const formStats = calculateFormStats(currentFormData, sampleSchema.schema);
    setStats(formStats);
  }, [currentFormData]);

  // Demo functions
  const demoBasicOperations = () => {
    console.group('🔍 Basic Form Data Operations');
    
    // Convert array to map
    const formMap = formDataArrayToMap(currentFormData);
    console.log('Form Data Map:', formMap);
    
    // Get flat data (just values)
    const flatData = getFlatFormData(currentFormData);
    console.log('Flat Form Data:', flatData);
    
    // Get field statistics
    console.log('Form Statistics:', stats);
    
    // Check if form is complete
    const isComplete = isFormComplete(currentFormData, sampleSchema.schema);
    console.log('Is Form Complete:', isComplete);
    
    console.groupEnd();
  };

  const demoFiltering = () => {
    console.group('🔍 Form Data Filtering');
    
    // Get filled fields
    const filledFields = filterFieldsWithValues(currentFormData);
    console.log('Filled Fields:', filledFields);
    
    // Get empty fields
    const emptyFields = filterEmptyFields(currentFormData);
    console.log('Empty Fields:', emptyFields);
    
    // Search fields
    const searchResults = searchFields(currentFormData, '日期');
    console.log('Search Results for "日期":', searchResults);
    
    console.groupEnd();
  };

  const demoExporting = () => {
    console.group('📤 Form Data Export');
    
    const exportOptions: IFormDataExportOptions = {
      includeExtendValue: true,
      includeEmpty: false,
      format: 'array'
    };
    
    // Export to JSON
    const jsonData = exportToJSON(currentFormData, exportOptions);
    console.log('JSON Export:', jsonData);
    
    // Export to CSV
    const csvData = exportToCSV(currentFormData, exportOptions);
    console.log('CSV Export:', csvData);
    
    console.groupEnd();
  };

  const demoComparison = () => {
    console.group('🔄 Form Data Comparison');
    
    // Compare current data with original
    const diff = compareFormData(originalFormData, currentFormData);
    console.log('Form Data Diff:', diff);
    
    // Generate summary
    const summary = generateFieldSummary(currentFormData);
    console.log('Form Summary:', summary);
    
    console.groupEnd();
  };

  const demoTypeChecking = () => {
    console.group('✅ Type-Safe Operations');
    
    // Type-safe field access
    const textField = currentFormData.find(f => f.key === 'TextField-K2AD4O5B') as ITextFieldData;
    if (textField) {
      console.log('Text Field Value:', textField.value);
      console.log('Text Field Type:', typeof textField.value); // string
    }
    
    const selectField = currentFormData.find(f => f.key === 'DDSelectField_1Y7FJU91S6L1C') as ISelectFieldData;
    if (selectField) {
      console.log('Select Field Value:', selectField.value);
      console.log('Select Field Extended Value:', selectField.extendValue);
    }
    
    console.groupEnd();
  };

  const simulateFormChanges = () => {
    const newData = [...currentFormData];
    
    // Simulate changing a text field
    const textFieldIndex = newData.findIndex(f => f.key === 'TextField-K2AD4O5B');
    if (textFieldIndex !== -1) {
      newData[textFieldIndex] = {
        key: newData[textFieldIndex].key,
        label: newData[textFieldIndex].label,
        value: `Modified at ${new Date().toLocaleTimeString()}`
      };
    }
    
    // Add a new field
    newData.push({
      key: 'demo_field_' + Date.now(),
      label: 'Demo Field',
      value: 'Demo Value'
    });
    
    setCurrentFormData(newData);
  };

  const filteredData = searchTerm 
    ? searchFields(currentFormData, searchTerm)
    : currentFormData;

  return (
    <div className="form-data-demo">
      <h1>🚀 SW AI Federated Modules - Form Data Demo</h1>
      
      <div className="demo-controls">
        <div className="search-section">
          <input
            type="text"
            placeholder="Search form data..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        
        <div className="demo-buttons">
          <button onClick={demoBasicOperations}>Basic Operations</button>
          <button onClick={demoFiltering}>Filtering</button>
          <button onClick={demoExporting}>Export Data</button>
          <button onClick={demoComparison}>Compare Data</button>
          <button onClick={demoTypeChecking}>Type Checking</button>
          <button onClick={simulateFormChanges}>Simulate Changes</button>
        </div>
      </div>

      {stats && (
        <div className="stats-panel">
          <h3>📊 Form Statistics</h3>
          <div className="stats-grid">
            <div className="stat-item">
              <label>Total Fields:</label>
              <span>{stats.totalFields}</span>
            </div>
            <div className="stat-item">
              <label>Filled Fields:</label>
              <span>{stats.filledFields}</span>
            </div>
            <div className="stat-item">
              <label>Required Fields:</label>
              <span>{stats.requiredFields}</span>
            </div>
            <div className="stat-item">
              <label>Completion Rate:</label>
              <span>{stats.completionRate.toFixed(1)}%</span>
            </div>
          </div>
        </div>
      )}

      <div className="demo-tabs">
        <div className="tab-buttons">
          <button 
            className={selectedDemo === 'overview' ? 'active' : ''}
            onClick={() => setSelectedDemo('overview')}
          >
            Overview
          </button>
          <button 
            className={selectedDemo === 'fields' ? 'active' : ''}
            onClick={() => setSelectedDemo('fields')}
          >
            Field Details
          </button>
          <button 
            className={selectedDemo === 'types' ? 'active' : ''}
            onClick={() => setSelectedDemo('types')}
          >
            Type Examples
          </button>
        </div>

        <div className="tab-content">
          {selectedDemo === 'overview' && (
            <div className="overview-panel">
              <h3>📋 Form Data Overview</h3>
              <p>This demo shows {currentFormData.length} form fields with various data types:</p>
              <ul>
                <li><strong>Text Fields:</strong> Single/multi-line input</li>
                <li><strong>Number Fields:</strong> Numeric input with validation</li>
                <li><strong>Select Fields:</strong> Single/multi-select with options</li>
                <li><strong>Date Fields:</strong> Date/time pickers</li>
                <li><strong>Address Fields:</strong> Location data</li>
                <li><strong>Money Fields:</strong> Currency input</li>
                <li><strong>DingTalk Integration:</strong> Employee and department data</li>
              </ul>
              
              <div className="quick-stats">
                <p>🔍 Search results: <strong>{filteredData.length}</strong> fields</p>
                <p>✅ Form completion: <strong>{stats?.completionRate.toFixed(1)}%</strong></p>
              </div>
            </div>
          )}

          {selectedDemo === 'fields' && (
            <div className="fields-panel">
              <h3>🔍 Field Details ({filteredData.length} fields)</h3>
              <div className="fields-list">
                {filteredData.map((field) => (
                  <div key={field.key} className="field-card">
                    <div className="field-header">
                      <h4>{field.label || field.key}</h4>
                      <span className="field-type">
                        {Array.isArray(field.value) ? 'Array' : typeof field.value}
                      </span>
                    </div>
                    <div className="field-content">
                      <div className="field-value">
                        <strong>Value:</strong> 
                        <code>{JSON.stringify(field.value)}</code>
                      </div>
                      {field.extendValue && (
                        <div className="field-extend">
                          <strong>Extended:</strong> 
                          <code>{JSON.stringify(field.extendValue)}</code>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {selectedDemo === 'types' && (
            <div className="types-panel">
              <h3>🏗️ TypeScript Type Examples</h3>
              <div className="code-examples">
                <div className="code-block">
                  <h4>Text Field Type</h4>
                  <pre>{`interface ITextFieldData extends IFormFieldData {
  value: string;
  extendValue?: undefined;
}`}</pre>
                </div>
                
                <div className="code-block">
                  <h4>Select Field Type</h4>
                  <pre>{`interface ISelectFieldData extends IFormFieldData {
  value: string;
  extendValue: ISelectOption;
}`}</pre>
                </div>
                
                <div className="code-block">
                  <h4>Usage Example</h4>
                  <pre>{`// Type-safe field access
const textField = formData.find(f => 
  f.key === 'TextField-K2AD4O5B'
) as ITextFieldData;

// TypeScript knows this is a string
console.log(textField.value.toUpperCase());`}</pre>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="footer">
        <p>💡 Check the browser console for detailed logs when clicking demo buttons!</p>
        <p>🔧 This demo showcases the enhanced type system for DingTalk OA form integration.</p>
      </div>
    </div>
  );
};
