/**
 * @fileoverview Utility functions for working with form data and context
 */

import { 
  IFormDataArray, 
  IFormDataMap, 
  IFormFieldDataUnion, 
  IFormDataExportOptions,
  IFormDataStats
} from '../types/form-data.type';

// ============================================================================
// FORM DATA TRANSFORMATION UTILITIES
// ============================================================================

/**
 * Convert form data array to map
 */
export const formDataArrayToMap = (formData: IFormDataArray): IFormDataMap => {
  return formData.reduce((map, fieldData) => {
    if (fieldData.key) {
      map[fieldData.key] = fieldData;
    }
    return map;
  }, {} as IFormDataMap);
};

/**
 * Convert form data map to array
 */
export const formDataMapToArray = (formDataMap: IFormDataMap): IFormDataArray => {
  return Object.values(formDataMap);
};

/**
 * Get flat key-value pairs from form data
 */
export const getFlatFormData = (formData: IFormDataArray): Record<string, any> => {
  return formData.reduce((flat, fieldData) => {
    if (fieldData.key && fieldData.value !== undefined) {
      flat[fieldData.key] = fieldData.value;
    }
    return flat;
  }, {} as Record<string, any>);
};

/**
 * Get form data with extended values as flat structure
 */
export const getFlatFormDataWithExtended = (formData: IFormDataArray): Record<string, any> => {
  return formData.reduce((flat, fieldData) => {
    if (fieldData.key) {
      flat[fieldData.key] = {
        value: fieldData.value,
        extendValue: fieldData.extendValue,
        label: fieldData.label
      };
    }
    return flat;
  }, {} as Record<string, any>);
};

// ============================================================================
// FORM DATA FILTERING UTILITIES
// ============================================================================

/**
 * Filter form data by field types
 */
export const filterByFieldType = (formData: IFormDataArray, fieldTypes: string[]): IFormDataArray => {
  return formData.filter(fieldData => {
    // Extract component type from field key pattern
    const componentType = extractComponentTypeFromKey(fieldData.key);
    return fieldTypes.includes(componentType);
  });
};

/**
 * Filter form data by field keys
 */
export const filterByFieldKeys = (formData: IFormDataArray, fieldKeys: string[]): IFormDataArray => {
  return formData.filter(fieldData => fieldKeys.includes(fieldData.key));
};

/**
 * Filter required fields only
 */
export const filterRequiredFields = (formData: IFormDataArray, schema: any[]): IFormDataArray => {
  const requiredFieldKeys = schema
    .filter(component => component.props?.required)
    .map(component => component.props?.id);
  
  return formData.filter(fieldData => requiredFieldKeys.includes(fieldData.key));
};

/**
 * Filter fields with values only
 */
export const filterFieldsWithValues = (formData: IFormDataArray): IFormDataArray => {
  return formData.filter(fieldData => {
    const value = fieldData.value;
    if (value === null || value === undefined || value === '') return false;
    if (Array.isArray(value) && value.length === 0) return false;
    return true;
  });
};

/**
 * Filter empty fields
 */
export const filterEmptyFields = (formData: IFormDataArray): IFormDataArray => {
  return formData.filter(fieldData => {
    const value = fieldData.value;
    if (value === null || value === undefined || value === '') return true;
    if (Array.isArray(value) && value.length === 0) return true;
    return false;
  });
};

// ============================================================================
// FORM DATA SEARCH UTILITIES
// ============================================================================

/**
 * Search fields by label or value
 */
export const searchFields = (
  formData: IFormDataArray, 
  searchTerm: string, 
  searchIn: 'label' | 'value' | 'both' = 'both'
): IFormDataArray => {
  const term = searchTerm.toLowerCase();
  
  return formData.filter(fieldData => {
    let matches = false;
    
    if (searchIn === 'label' || searchIn === 'both') {
      const label = fieldData.label?.toString().toLowerCase() || '';
      matches = matches || label.includes(term);
    }
    
    if (searchIn === 'value' || searchIn === 'both') {
      const value = String(fieldData.value || '').toLowerCase();
      matches = matches || value.includes(term);
    }
    
    return matches;
  });
};

/**
 * Find field by key
 */
export const findFieldByKey = (formData: IFormDataArray, key: string): IFormFieldDataUnion | undefined => {
  return formData.find(fieldData => fieldData.key === key);
};

/**
 * Find fields by keys
 */
export const findFieldsByKeys = (formData: IFormDataArray, keys: string[]): (IFormFieldDataUnion | undefined)[] => {
  return keys.map(key => findFieldByKey(formData, key));
};

// ============================================================================
// FORM DATA VALIDATION UTILITIES
// ============================================================================

/**
 * Calculate form statistics
 */
export const calculateFormStats = (formData: IFormDataArray, schema: any[]): IFormDataStats => {
  const totalFields = formData.length;
  const filledFields = filterFieldsWithValues(formData).length;
  const requiredFields = filterRequiredFields(formData, schema).length;
  const completionRate = totalFields > 0 ? (filledFields / totalFields) * 100 : 0;
  
  return {
    totalFields,
    filledFields,
    requiredFields,
    invalidFields: 0, // Would need validation logic to determine this
    completionRate: Math.round(completionRate * 100) / 100
  };
};

/**
 * Check if form is complete (all required fields filled)
 */
export const isFormComplete = (formData: IFormDataArray, schema: any[]): boolean => {
  const requiredFieldKeys = schema
    .filter(component => component.props?.required)
    .map(component => component.props?.id);
  
  return requiredFieldKeys.every(key => {
    const fieldData = findFieldByKey(formData, key);
    if (!fieldData) return false;
    
    const value = fieldData.value;
    if (value === null || value === undefined || value === '') return false;
    if (Array.isArray(value) && value.length === 0) return false;
    
    return true;
  });
};

// ============================================================================
// FORM DATA EXPORT UTILITIES
// ============================================================================

/**
 * Export form data to JSON
 */
export const exportToJSON = (formData: IFormDataArray, options?: IFormDataExportOptions): string => {
  let dataToExport = formData;
  
  if (options?.excludeFields) {
    dataToExport = dataToExport.filter(field => !options.excludeFields!.includes(field.key));
  }
  
  if (options?.includeFields) {
    dataToExport = dataToExport.filter(field => options.includeFields!.includes(field.key));
  }
  
  if (!options?.includeEmpty) {
    dataToExport = filterFieldsWithValues(dataToExport);
  }
  
  const exportData = dataToExport.map(field => ({
    key: field.key,
    label: field.label,
    value: field.value,
    ...(options?.includeExtendValue && field.extendValue && { extendValue: field.extendValue })
  }));
  
  return JSON.stringify(exportData, null, 2);
};

/**
 * Export form data to CSV
 */
export const exportToCSV = (formData: IFormDataArray, options?: IFormDataExportOptions): string => {
  let dataToExport = formData;
  
  if (options?.excludeFields) {
    dataToExport = dataToExport.filter(field => !options.excludeFields!.includes(field.key));
  }
  
  if (options?.includeFields) {
    dataToExport = dataToExport.filter(field => options.includeFields!.includes(field.key));
  }
  
  if (!options?.includeEmpty) {
    dataToExport = filterFieldsWithValues(dataToExport);
  }
  
  const headers = ['Field Key', 'Label', 'Value'];
  const rows = dataToExport.map(field => [
    field.key,
    field.label || '',
    Array.isArray(field.value) ? field.value.join(';') : String(field.value || '')
  ]);
  
  return [headers, ...rows]
    .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
    .join('\n');
};

// ============================================================================
// HELPER UTILITIES
// ============================================================================

/**
 * Extract component type from field key
 */
export const extractComponentTypeFromKey = (key: string): string => {
  if (key === 'dingtalk_origin_dept') return 'DingTalkOriginDept';
  
  const match = key.match(/^([A-Za-z]+)/);
  return match ? match[1] : 'Unknown';
};

/**
 * Generate field summary for debugging
 */
export const generateFieldSummary = (formData: IFormDataArray): string => {
  return formData
    .map(field => `${field.key}: ${field.label || 'No Label'} = ${JSON.stringify(field.value)}`)
    .join('\n');
};

/**
 * Deep clone form data
 */
export const cloneFormData = (formData: IFormDataArray): IFormDataArray => {
  return JSON.parse(JSON.stringify(formData));
};

/**
 * Compare two form data arrays for differences
 */
export const compareFormData = (
  formData1: IFormDataArray, 
  formData2: IFormDataArray
): Record<string, { initial: any; current: any; changed: boolean }> => {
  const map1 = formDataArrayToMap(formData1);
  const map2 = formDataArrayToMap(formData2);
  const allKeys = new Set([...Object.keys(map1), ...Object.keys(map2)]);
  
  const diff: Record<string, { initial: any; current: any; changed: boolean }> = {};
  
  allKeys.forEach(key => {
    const initial = map1[key]?.value;
    const current = map2[key]?.value;
    const changed = JSON.stringify(initial) !== JSON.stringify(current);
    
    diff[key] = { initial, current, changed };
  });
  
  return diff;
};
