import React from 'react';
import { AIFormFill } from '../components/ai-form-fill.tsx/ai-form-fill';
import { Context } from '../types/render-context.type';

// Entry point for the AI Form Fill federated module
export const AIFormFillEntry: React.FC = () => {
  // In a real DingTalk environment, the context would be available from window.__ctx
  // For demo purposes, we'll check if it exists
  const context = (window as any).__ctx as Context | undefined;

  return (
    <AIFormFill 
      context={context} 
      debug={process.env.NODE_ENV === 'development'} 
    />
  );
};

// Export for module federation
export default AIFormFillEntry;
