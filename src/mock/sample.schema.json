{"schema": [{"componentName": "TextField", "props": {"placeholder": "请输入", "label": "单行输入框", "id": "TextField-K2AD4O5B", "bizAlias": "", "defaultValue": "你好，Arno", "rely": {}, "fields": [], "dataSource": {}}}, {"componentName": "TextareaField", "props": {"label": "多行输入框", "placeholder": "请输入", "required": true, "id": "TextareaField_110SFYLNA8PA8", "bizAlias": "", "defaultValue": "你好，世界", "rely": {}, "fields": [], "dataSource": {}}}, {"componentName": "ColumnLayout", "props": {"label": "分栏", "notPrint": "1", "id": "ColumnLayout_180BE1LEXZRPC", "group": ["TextField-K2AD4O5B", "TextareaField_110SFYLNA8PA8"]}}, {"componentName": "NumberField", "props": {"label": "数字输入框", "placeholder": "请输入数字", "required": true, "ratio": 50, "id": "NumberField_1D5JDEQLZM680", "bizAlias": "", "unit": "$", "precision": 2, "defaultValue": "12", "rely": {}, "fields": [], "dataSource": {}}}, {"componentName": "DDSelectField", "props": {"label": "单选框", "placeholder": "请选择", "options": [{"key": "option_0", "value": "选项1"}, {"key": "option_1", "value": "选项2"}, {"key": "option_2", "value": "选项3"}], "required": true, "spread": true, "ratio": 50, "id": "DDSelectField_1Y7FJU91S6L1C", "bizAlias": "", "behaviorLinkage": [], "defaultValue": "选项1", "defaultExtendValue": {"key": "option_0", "label": "选项1"}, "rely": {}, "fields": [], "dataSource": {}, "notPrint": "1"}}, {"componentName": "DDMultiSelectField", "props": {"label": "多选框", "placeholder": "请选择", "options": [{"key": "option_0", "value": "选项1"}, {"key": "option_1", "value": "选项2"}, {"key": "option_2", "value": "选项3"}], "required": false, "spread": false, "ratio": 50, "id": "DDMultiSelectField_1UE23J2DT3OJK", "bizAlias": "", "behaviorLinkage": [], "defaultValue": ["选项2", "选项3"], "defaultExtendValue": [{"key": "option_1"}, {"key": "option_2"}], "rely": {}, "fields": [], "dataSource": {}}}, {"componentName": "DDDateField", "props": {"label": "日期", "placeholder": "请选择", "format": "yyyy-MM-dd HH:mm", "unit": "小时", "required": false, "id": "DDDateField_1DNGPEEYEZPXC", "bizAlias": "", "defaultValue": "2025-06-05 04:00", "rely": {}, "fields": [], "dataSource": {}}}, {"componentName": "DDDateField", "props": {"label": "日期（年月日）", "placeholder": "请选择", "format": "yyyy-MM-dd", "unit": "天", "required": false, "id": "DDDateField_1DT863BBVSUF4", "bizAlias": "", "defaultValue": "2025-06-06", "rely": {}, "fields": [], "dataSource": {}}}, {"componentName": "IdCardField", "props": {"label": "身份证（启用敏感信息隐藏）", "placeholder": "请输入", "required": false, "id": "IdCardField_13LKMYK8ZC0E8", "bizAlias": "", "mask": true}}, {"componentName": "PhoneField", "props": {"label": "电话（手机 + 固话）兼容", "placeholder": "请输入", "mode": "phone_tel", "required": false, "id": "PhoneField_YTQ1NIZQTGQO", "bizAlias": ""}}, {"componentName": "PhoneField", "props": {"label": "手机", "placeholder": "请输入", "mode": "phone", "required": false, "id": "PhoneField_1G213ABPNAIO0", "bizAlias": ""}}, {"componentName": "PhoneField", "props": {"label": "固话", "placeholder": "请输入", "mode": "phone", "required": false, "id": "PhoneField_1SYGYV4JR5534", "bizAlias": "", "mask": true}}, {"componentName": "DDDateRangeField", "props": {"label": ["开始时间", "结束时间"], "placeholder": "请选择", "format": "yyyy-MM-dd", "unit": "天", "required": false, "duration": true, "durationLabel": "时长", "id": "DDDateRangeField_13NU9IPO9T3I8", "bizAlias": ""}}, {"componentName": "TextNote", "props": {"content": "请输入说明文字（飘红）", "notPrint": "0", "id": "TextNote_GTZEMYDJJI0W", "bizAlias": "", "link": "https://www.dingtalk.com", "style": {"color": "#F25643"}}}, {"componentName": "TextNote", "props": {"content": "请输入说明文字（审批不显示）", "notPrint": "0", "id": "TextNote_VVYXO14LX2IO", "bizAlias": "", "link": "https://www.dingtalk.com", "hiddenInApprovalDetail": true}}, {"componentName": "MoneyField", "props": {"label": "金额（元）", "placeholder": "请输入金额", "notUpper": "0", "required": false, "id": "MoneyField_16RI5M0HL3Y0W", "bizAlias": "", "precision": 2}}, {"componentName": "DDPhotoField", "props": {"label": "图片", "watermark": false, "required": false, "id": "DDPhotoField_22D813VSE7IM8", "bizAlias": "", "maxUpload": 2}}, {"componentName": "DDPhotoField", "props": {"label": "图片（水印照片）", "watermark": true, "required": false, "id": "DDPhotoField_10RL7KQZPIVB4", "bizAlias": "", "maxUpload": 2}}, {"componentName": "CascadeField", "props": {"label": "产品分类", "require": true, "placeholder": "请输入", "dataSource": {"type": "db_table", "target": {"appUuid": "", "sourceForm": "oa", "appId": "-4", "scene": "CascadeField_7307321", "initScene": "CascadeField_7307321"}}, "id": "CascadeField_J7AAVCFKPMV4", "bizAlias": ""}}, {"componentName": "CascadeField", "props": {"label": "产品分类", "require": true, "placeholder": "请输入", "dataSource": {"type": "db_table", "target": {"appUuid": "", "appId": "-4", "sourceForm": "oa", "scene": "CascadeField_7184310", "initScene": "CascadeField_7184310"}}, "id": "CascadeField_11QZJ4Y7K6O74", "bizAlias": ""}}, {"componentName": "TableField", "children": [{"componentName": "TextField", "props": {"label": "单行输入框", "placeholder": "请输入", "required": false, "ratio": 50, "id": "TextField_CD3Y88M5E0HS"}}, {"componentName": "TextareaField", "props": {"label": "多行输入框", "placeholder": "请输入", "required": false, "id": "TextareaField_NRV9WC72GIKG"}}], "props": {"label": "表格", "actionName": "添加", "tableViewMode": "table", "id": "TableField_1WVUH2SFIZUO0"}}, {"componentName": "DDAttachment", "props": {"label": "附件", "required": false, "id": "DDAttachment_1F4JEDUAGT7NK", "bizAlias": "", "appId": "", "needPreSign": false, "eSign": false, "dingEsign": false, "customAttrs": {"dingEsign": false}}}, {"componentName": "InnerContactField", "props": {"label": "联系人（单人）", "placeholder": "请选择", "choice": "0", "required": false, "id": "InnerContactField_1Z8YKQ4V3D7UO", "bizAlias": ""}}, {"componentName": "InnerContactField", "props": {"label": "联系人（多人）", "placeholder": "请选择", "choice": "1", "required": false, "id": "InnerContactField_HF9R2IS0TYIO", "bizAlias": ""}}, {"componentName": "ExternalContactField", "props": {"label": "外部联系人", "placeholder": "请选择", "required": false, "id": "ExternalContactField_AALZKS1SNRB4"}}, {"componentName": "DepartmentField", "props": {"label": "部门", "placeholder": "请选择", "multiple": false, "required": false, "showDeptFullName": false, "id": "DepartmentField_1TY9RHWFZRSHS"}}, {"componentName": "TimeAndLocationField", "props": {"required": false, "label": ["当前时间", "当前地点"], "id": "TimeAndLocationField_PV4BCDBKH8N4"}}, {"componentName": "AddressField", "props": {"label": "省市区（街道）", "needDetail": true, "required": false, "id": "AddressField_17VDOA9OVFZ4", "bizAlias": ""}}, {"componentName": "AddressField", "props": {"label": "省市区", "needDetail": false, "required": false, "id": "AddressField_10MGAME3F1KAO", "bizAlias": ""}}, {"componentName": "AddressField", "props": {"label": "省市", "needDetail": 0, "required": false, "id": "AddressField_G6PDNS571QM8", "bizAlias": ""}}, {"componentName": "StarRatingField", "props": {"label": "评分 10 分制度", "placeholder": "请输入", "limit": 10, "required": false, "id": "StarRatingField_1TKGPTWL6RU9S", "bizAlias": ""}}, {"componentName": "StarRatingField", "props": {"label": "评分 5 分制度", "placeholder": "请输入", "limit": 5, "required": false, "id": "StarRatingField_1UCPCWFRYRXFK", "bizAlias": ""}}]}