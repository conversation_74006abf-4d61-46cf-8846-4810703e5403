import React, { ReactElement } from 'react';
import { 
  IFormDataArray, 
  IFormDataMap, 
  IFormFieldDataUnion, 
  IFormValidationResult, 
  IFormDataExportOptions,
  IFormDataFilterOptions,
  IFormDataStats
} from './form-data.type';

/** query form data res */
export interface QueryFormDataRes {
  list: List[];
  totalCount: number;
  hasMore: boolean;
  nextCursor: number;
}

export interface List {
  formCode: string;
  deptName: string;
  formInstanceId: string;
  outInstanceId: string;
  schema: string;
  title: string;
  createTime: number;
  creator: string;
  attributes: Attributes;
  dataArray: DataArray[];
  appUuid: string;
  abstractMessage: string;
  modifyTime: number;
  orgId: number;
}

export interface Attributes {
  authUserType: AuthUserType;
}

export enum AuthUserType {
  Dduid = 'DDUID',
}

export interface DataArray {
  label: string;
  key: string;
  bizAlias: string;
  value?: string;
  componentType: string;
}

/**
 * @fileoverview Type definitions for the Context class and related interfaces
 * This file provides comprehensive type definitions for external usage of the Context class
 * from the swap-render-engine project.
 *
 * <AUTHOR> Render Engine Team
 * @version 1.0.0
 */


// ============================================================================
// CORE INTERFACES AND TYPES
// ============================================================================

/**
 * Form layout orientation
 */
export type FormLayout = 'horizontal' | 'vertical';

/**
 * Field validation function type
 * @param props - Field properties including label, value, and extendValue
 * @param param - Additional validation parameters
 * @returns Validation result - true for valid, string for error message, or Promise for async validation
 */
export type IValidationFunc = (
  props: { label?: string | string[]; value?: any; extendValue?: any },
  param?: any,
) => boolean | string | Promise<boolean | string> | undefined;

/**
 * Validation rule configuration
 */
export interface IValidationRule {
  /** Rule type identifier */
  type?: string;
  /** Validation parameters */
  param?: any;
  /** Error message to display when validation fails */
  message?: string;
  /** Custom validation function */
  func?: IValidationFunc;
}

/**
 * Component properties interface
 * Defines the structure of props that can be passed to form components
 */
export interface IComponentProps {
  /** Unique identifier for the component */
  id: string;
  /** Business type identifier */
  bizType?: string;
  /** Business alias for the component */
  bizAlias?: string;
  /** Whether the component is in view-only mode */
  viewMode?: boolean;
  /** Whether the component is hidden */
  hidden?: boolean;
  /** Whether the component is invisible */
  invisible?: boolean;
  /** Whether the component is disabled */
  disabled?: boolean;
  /** Display format for the component */
  format?: string;
  /** Component label */
  label?: any;
  /** Placeholder text */
  placeholder?: any;
  /** Default value */
  defaultValue?: any;
  /** Current value */
  value?: any;
  /** Child components */
  children?: object[];
  /** Field dependency configuration */
  rely?: {
    type: 'formula' | 'async' | 'rely';
    fields?: string[];
    formula?: string;
    formulaId?: string;
    version?: 1 | 2;
  };
  /** Additional properties */
  [propName: string]: any;
}

/**
 * Component schema definition
 * Describes the structure and configuration of a form component
 */
export interface IComponentSchema {
  /** Component type name */
  componentName: string;
  /** Component value */
  value?: any;
  /** Extended value for complex components */
  extendValue?: any;
  /** Component properties */
  props: IComponentProps;
  /** Child component schemas */
  children?: IComponentSchema[];
}

/**
 * Field data structure
 * Represents the data associated with a form field
 */
export interface IFieldData<V = any, Ext = any> {
  /** Field identifier */
  key: string;
  /** Field value */
  value?: V;
  /** Extended value */
  extendValue?: Ext;
  /** Whether the data is masked/encrypted */
  mask?: boolean;
  /** Data type indicator */
  type?: 'defaultValue';
}

/**
 * Field data map type
 * Maps field keys to their data
 */
export interface IFieldDataMap {
  [key: string]: IFieldData;
}

/**
 * Field value export format
 * Used when exporting field values from the form
 */
export interface IFieldValue {
  /** Field key */
  key: string;
  /** Field label */
  label?: string;
  /** Business alias */
  bizAlias?: string;
  /** Field value */
  value?: any;
  /** Extended value */
  extendValue?: any;
  /** Component name */
  componentName?: string;
}

/**
 * Data input type
 * Can be either a map or array of field data
 */
export type IData = IFieldDataMap | IFieldData[];

/**
 * React component map interface
 * Maps component names to their React component implementations
 */
export interface IReactComponentMap {
  [componentName: string]: React.ComponentType & {
    validation: Array<IValidationRule | IValidationRule['func']>;
  };
}

/**
 * Global validation rule
 */
export interface IGlobalValidateRule {
  /** Field ID to validate */
  fieldId: string;
}

/**
 * Global validation configuration
 */
export interface IGlobalValidate {
  /** Validation type */
  validType: 'NSelectOne';
  /** Validation rules */
  rules: IGlobalValidateRule[];
  /** Error message */
  message: string;
}

/**
 * Form hook configuration
 * Defines automated actions triggered by form events
 */
export interface IFormHook {
  /** Trigger configuration */
  trigger: {
    type: 'init' | 'editInit' | 'onValueChange' | 'beforeSelected' | 'onSubmit' | 'onTableAdd';
    field: string;
    fields: string[];
  };
  /** Data source configuration */
  dataSource: {
    type: 'connector';
    target: {
      actionInstanceId?: string;
      actionId: string;
      connectorId: string;
    };
    params: {
      req: Array<{ param: string; valueType: string; value: string }>;
    };
    resp: Array<{ fieldId: string; param: string }>;
  };
}

/**
 * Data masking configuration
 * Used for handling encrypted/sensitive data
 */
export interface IMaskConfig {
  /** Function to fetch decrypted data */
  fetchData: (params: {
    componentId: string;
    label: string;
  }) => Promise<IFieldData>;
}

/**
 * Field search parameters
 * Used when searching for fields within the context
 */
export interface IGetFieldExtraParams {
  /** Whether to search in table children (default: true) */
  tableChildren?: boolean;
  /** Whether to search in parent context (default: false) */
  parent?: boolean;
}

/**
 * Root store interface
 * Defines the interface for the root store object
 */
export interface IRootStore {
  /** Function to update components */
  updateComponent: () => void;
}

/**
 * Pending operations map
 * Tracks ongoing asynchronous operations
 */
export interface IPendingMap {
  [key: string]: string;
}

/**
 * Global rule map
 * Maps rule keys to their boolean states
 */
export interface IGlobalRuleMap {
  [key: string]: boolean;
}

// ============================================================================
// COMPONENT STORE TYPES
// ============================================================================

/**
 * Base field store interface
 * Defines common methods available on all field stores
 */
export interface IFieldStore {
  /** Get field ID */
  getFieldId(): string;
  /** Get component name */
  getComponentName(): string;
  /** Get field value */
  getValue(): any;
  /** Set field value */
  setValue(value: any, extra?: { silent?: boolean }): void;
  /** Get extended value */
  getExtendValue(): any;
  /** Set extended value */
  setExtendValue(extendValue: any): void;
  /** Get field properties */
  getProps(): IComponentProps;
  /** Get specific property */
  getProp(propName: string): any;
  /** Validate field */
  validate(): boolean | string | { valid: boolean; message: string } | Promise<{ valid: boolean; message: string }>;
  /** Show field */
  show(): void;
  /** Hide field */
  hide(): void;
  /** Export field value */
  exportValue(config?: { componentNameEnable?: boolean; includeHiddenField?: boolean; includeEmpty?: boolean }): IFieldValue;
  /** Register value change callback */
  onValueChange(callback: (value: any) => void): () => void;
}

/**
 * Table field store interface
 * Extends field store with table-specific methods
 */
export interface ITableFieldStore extends IFieldStore {
  /** Get table rows */
  getRows(): any[];
  /** Get table data */
  getData(): any[];
  /** Register change callback */
  onChange(callback: () => void): void;
}

/**
 * Container store interface
 * For components that contain other components
 */
export interface IContainerStore extends IFieldStore {
  /** Get child fields */
  getChildren(): IFieldStore[];
}

/**
 * Component store union type
 * Represents any type of field store
 */
export type IComponentStore = IFieldStore | ITableFieldStore | IContainerStore;

// ============================================================================
// CONTEXT CONFIGURATION
// ============================================================================

/**
 * Context properties interface
 * Defines all configuration options for creating a Context instance
 */
export interface IContextProps {
  /** Map of available React components */
  components?: IReactComponentMap;
  /** Enable debug mode */
  debugMode?: boolean;
  /** Form schema definition */
  schema: IComponentSchema | IComponentSchema[];
  /** Additional schema to merge */
  mergeSchema?: any[];
  /** Original schema backup */
  originalSchema?: IComponentSchema[];
  /** Global validation rules */
  globalValidate?: IGlobalValidate[];
  /** Initial form data */
  data?: IData;
  /** Custom render interceptor */
  renderIntercept?: (elem: React.ReactElement, store: IComponentStore) => ReactElement;
  /** View-only mode */
  viewMode?: boolean;
  /** @deprecated Use viewMode instead */
  readOnly?: boolean;
  /** Show built-in error messages */
  showBuiltinErrorMsg?: boolean;
  /** Hide empty fields in view mode (default: true) */
  hideEmptyInViewMode?: boolean;
  /** Disable all fields */
  disabled?: boolean;
  /** Form layout orientation */
  layout?: FormLayout;
  /** Form hooks configuration */
  hooks?: IFormHook[];
  /** Auto-check fields list */
  autoCheckFields?: string[];
  /** CSS class name */
  className?: string;
  /** Global properties for all components */
  globalProps?: object;
  /** Initialization callback */
  onInit?: (ctx: Context) => void;
  /** @deprecated Root store object */
  rootStore?: any;
  /** Context parameters */
  params?: {
    /** Form scene type */
    scene?: 'create' | 'detail' | 'edit';
    /** Sub-scene type */
    subScene?: 'tableRowEdit';
    /** Form status */
    status: 'RUNNING' | 'COMPLETED';
    /** Additional parameters */
    [key: string]: any;
  };
  /** Parent context reference */
  parentCtx?: Context;
  /** Field settings configuration */
  fieldSettings?: any;
  /** Show label index numbers */
  showLabelIndex?: boolean;
  /** Data masking configuration */
  maskConfig?: IMaskConfig;
  /** Additional properties */
  [key: string]: any;
}

// ============================================================================
// CONTEXT CLASS DEFINITION
// ============================================================================

/**
 * Context class - Main form rendering engine
 *
 * The Context class is the core of the swap-render-engine. It manages form schemas,
 * field data, validation, and rendering. It provides a comprehensive API for
 * creating and managing dynamic forms.
 *
 * @example
 * ```typescript
 * import Context, { IContextProps } from './types/Context';
 *
 * const props: IContextProps = {
 *   schema: {
 *     componentName: 'TextField',
 *     props: {
 *       id: 'name',
 *       label: 'Name',
 *       placeholder: 'Enter your name'
 *     }
 *   },
 *   data: [{ key: 'name', value: 'John Doe' }]
 * };
 *
 * const context = new Context(props);
 * ```
 */
export declare class Context extends React.Component<IContextProps, {}> {
  // ============================================================================
  // STATIC PROPERTIES AND METHODS
  // ============================================================================

  /** Default props for Context component */
  static defaultProps: {
    layout: FormLayout;
  };

  /**
   * Create a new Context instance
   * @param props - Context configuration properties
   * @param container - Optional DOM container to render into
   * @returns New Context instance
   */
  static create(props: IContextProps, container?: HTMLElement): Context;

  // ============================================================================
  // INSTANCE PROPERTIES
  // ============================================================================

  /** Parent context reference */
  parentCtx?: Context;
  /** Form layout orientation */
  layout?: FormLayout;
  /** Root store object */
  rootStore?: object;
  /** Event emitter for internal communication */
  emitter: {
    on: (event: string, callback: (...args: any[]) => void) => void;
    off: (event: string, callback: (...args: any[]) => void) => void;
    emit: (event: string, ...args: any[]) => void;
  };
  /** Whether form is currently validating */
  isValidating: boolean;
  /** Debug mode flag */
  debugMode?: boolean;
  /** View-only mode flag */
  viewMode?: boolean;
  /** @deprecated Use viewMode instead */
  readOnly?: boolean;
  /** Hide empty fields in view mode */
  hideEmptyInViewMode?: boolean;
  /** Show built-in error messages */
  showBuiltinErrorMsg?: boolean;
  /** Context parameters */
  params?: IContextProps['params'];
  /** Form schema array */
  schema: any[];
  /** Merged schema array */
  mergeSchema: any[];
  /** Global validation rules */
  globalValidate?: IGlobalValidate[];
  /** Available React components */
  components: IReactComponentMap;
  /** Field store instances */
  fields: IComponentStore[];
  /** Plugin functions */
  plugins: Array<(ctx: Context) => void>;
  /** Form hooks */
  hooks: IFormHook[];
  /** Auto-check fields */
  autoCheckFields: string[];
  /** Global component properties */
  globalProps: any;
  /** Schema transformer */
  transformer: any;
  /** Event manager */
  eventManager: any;
  /** Event map */
  eventMap: any;
  /** Original schema backup */
  originalSchema?: any;
  /** Original data map */
  originalDataMap?: IFieldDataMap;
  /** Field settings */
  fieldSettings: any;
  /** Context index */
  index: number;
  /** Currently invalid field */
  invalidField?: IFieldStore | null;
  /** Initial form data map */
  initialFormDataMap: IFieldDataMap;
  /** Mixin business alias classes */
  mixinBizAliasClass: { [key: string]: any };
  /** Context ID */
  id?: number;
  /** Initialization flag */
  inited?: boolean;
  /** Data masking configuration */
  maskConfig?: IMaskConfig;
  /** Table field ID mapping */
  tableFieldIdMap: { [fieldId: string]: string };
  /** Pending operations map */
  pendingMap: IPendingMap;

  // ============================================================================
  // CONSTRUCTOR
  // ============================================================================

  /**
   * Create a new Context instance
   * @param props - Context configuration properties
   */
  constructor(props: IContextProps);

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================

  /**
   * Initialize the context with given properties
   * @param props - Context configuration properties
   */
  init(props: IContextProps): void;

  /**
   * Create a new Context instance based on current context
   * @param props - Additional properties for the new context
   * @returns New Context instance
   */
  create(props: IContextProps): Context;

  // ============================================================================
  // CONTEXT INFORMATION METHODS
  // ============================================================================

  /**
   * Get parent context
   * @returns Parent context instance or undefined
   */
  getParentCtx(): Context | undefined;

  /**
   * Check if context is in view mode
   * @returns True if in view mode
   */
  isViewMode(): boolean;

  // ============================================================================
  // FORM DATA METHODS
  // ============================================================================

  /**
   * Get all form data as an array (matches window.__ctx.getFormData() result)
   * @returns Promise resolving to form data array
   */
  getFormData(): Promise<IFormDataArray>;

  /**
   * Get form data as a key-value map
   * @returns Promise resolving to form data map
   */
  getFormDataMap(): Promise<IFormDataMap>;

  /**
   * Get form data with filtering and export options
   * @param options - Export and filter options
   * @returns Promise resolving to filtered form data
   */
  getFormDataWithOptions(options?: IFormDataExportOptions & IFormDataFilterOptions): Promise<IFormDataArray | IFormDataMap>;

  /**
   * Get specific field data by key
   * @param fieldKey - The field key to retrieve
   * @returns Field data or undefined if not found
   */
  getFieldData(fieldKey: string): IFormFieldDataUnion | undefined;

  /**
   * Get multiple field data by keys
   * @param fieldKeys - Array of field keys to retrieve
   * @returns Array of field data
   */
  getFieldsData(fieldKeys: string[]): (IFormFieldDataUnion | undefined)[];

  /**
   * Set field value
   * @param fieldKey - The field key to update
   * @param value - New value for the field
   * @param extendValue - Optional extended value
   * @returns Promise resolving to success status
   */
  setFieldValue(fieldKey: string, value: any, extendValue?: any): Promise<boolean>;

  /**
   * Set multiple field values
   * @param fieldData - Object mapping field keys to their new values
   * @returns Promise resolving to success status
   */
  setFieldsValue(fieldData: Record<string, { value: any; extendValue?: any }>): Promise<boolean>;

  /**
   * Clear field value
   * @param fieldKey - The field key to clear
   * @returns Promise resolving to success status
   */
  clearFieldValue(fieldKey: string): Promise<boolean>;

  /**
   * Clear all form data
   * @returns Promise resolving to success status
   */
  clearAllData(): Promise<boolean>;

  /**
   * Validate the entire form
   * @returns Promise resolving to validation result
   */
  validateForm(): Promise<IFormValidationResult>;

  /**
   * Validate specific field
   * @param fieldKey - The field key to validate
   * @returns Promise resolving to validation result for the field
   */
  validateField(fieldKey: string): Promise<{ isValid: boolean; error?: string }>;

  /**
   * Get form statistics
   * @returns Form completion and validation statistics
   */
  getFormStats(): IFormDataStats;

  /**
   * Get fields by component type
   * @param componentType - The component type (e.g., 'TextField', 'NumberField')
   * @returns Array of field data matching the component type
   */
  getFieldsByType(componentType: string): IFormFieldDataUnion[];

  /**
   * Get required fields
   * @returns Array of required field data
   */
  getRequiredFields(): IFormFieldDataUnion[];

  /**
   * Get empty/unfilled fields
   * @returns Array of empty field data
   */
  getEmptyFields(): IFormFieldDataUnion[];

  /**
   * Get filled fields
   * @returns Array of filled field data
   */
  getFilledFields(): IFormFieldDataUnion[];

  /**
   * Get invalid fields
   * @returns Promise resolving to array of invalid field data
   */
  getInvalidFields(): Promise<IFormFieldDataUnion[]>;

  /**
   * Check if field exists in the form
   * @param fieldKey - The field key to check
   * @returns True if field exists
   */
  hasField(fieldKey: string): boolean;

  /**
   * Get field count
   * @returns Total number of fields in the form
   */
  getFieldCount(): number;

  /**
   * Export form data to various formats
   * @param format - Export format ('json', 'csv', 'xml')
   * @param options - Export options
   * @returns Promise resolving to exported data string
   */
  exportFormData(format: 'json' | 'csv' | 'xml', options?: IFormDataExportOptions): Promise<string>;

  /**
   * Import form data from external source
   * @param data - Data to import (array or object)
   * @param options - Import options
   * @returns Promise resolving to success status
   */
  importFormData(data: IFormDataArray | IFormDataMap | any, options?: { merge?: boolean; validate?: boolean }): Promise<boolean>;

  /**
   * Reset form to initial state
   * @returns Promise resolving to success status
   */
  resetForm(): Promise<boolean>;

  /**
   * Get form data diff between current and initial state
   * @returns Object showing changed fields
   */
  getFormDataDiff(): Record<string, { 
    initial: any; 
    current: any; 
    changed: boolean 
  }>;

  /**
   * Subscribe to form data changes
   * @param callback - Callback function to execute on data change
   * @returns Unsubscribe function
   */
  onFormDataChange(callback: (changedFields: string[], formData: IFormDataArray) => void): () => void;

  /**
   * Subscribe to field value changes
   * @param fieldKey - Field key to watch
   * @param callback - Callback function to execute on field change
   * @returns Unsubscribe function
   */
  onFieldChange(fieldKey: string, callback: (newValue: any, oldValue: any, fieldData: IFormFieldDataUnion) => void): () => void;

  /**
   * Get field schema by key
   * @param fieldKey - The field key
   * @returns Field schema object or undefined
   */
  getFieldSchema(fieldKey: string): IComponentSchema | undefined;

  /**
   * Search fields by label or value
   * @param searchTerm - Search term
   * @param searchIn - Where to search ('label', 'value', 'both')
   * @returns Array of matching field data
   */
  searchFields(searchTerm: string, searchIn?: 'label' | 'value' | 'both'): IFormFieldDataUnion[];

  /**
   * Get form data as flat key-value pairs (excluding extendValue)
   * @returns Object with field keys and their simple values
   */
  getFlatFormData(): Record<string, any>;

  /**
   * Get form completion percentage
   * @returns Completion percentage (0-100)
   */
  getCompletionPercentage(): number;

  // ============================================================================
  // FIELD SEARCH METHODS
  // ============================================================================

  /**
   * Find field by property value
   * @param propName - Property name to search by
   * @param propValue - Property value to match
   * @param extra - Additional search parameters
   * @returns Matching field store or undefined
   */
  getFieldByProp(
    propName: string,
    propValue: any,
    extra?: IGetFieldExtraParams,
  ): IFieldStore | ITableFieldStore | undefined;

  /**
   * Find field by ID
   * @param id - Field ID to search for
   * @param extra - Additional search parameters
   * @returns Matching field store or undefined
   */
  getFieldById(id: string, extra?: IGetFieldExtraParams): IFieldStore | ITableFieldStore | undefined;

  /**
   * Find field by business alias
   * @param bizAlias - Business alias to search for
   * @param extra - Additional search parameters
   * @returns Matching field store or undefined
   */
  getFieldByBizAlias(bizAlias: any, extra?: IGetFieldExtraParams): IFieldStore | ITableFieldStore | undefined;

  /**
   * Find all fields by component name
   * @param componentName - Component name to search for
   * @returns Array of matching field stores
   */
  getFieldsByComponentName(componentName: string): IFieldStore[];

  // ============================================================================
  // DATA METHODS
  // ============================================================================

  /**
   * Get all form data including empty values
   * @param filter - Optional filter function
   * @param config - Configuration options
   * @returns Array of field values
   */
  getAllFormData(
    filter?: (item: IFieldStore | ITableFieldStore, fieldValue: IFieldValue) => boolean,
    config?: {
      includeHiddenField?: boolean;
      componentNameEnable?: boolean;
    },
  ): IFieldValue[];

  /**
   * Get form data excluding empty values
   * @param filter - Optional filter function
   * @param config - Configuration options
   * @returns Array of field values
   */
  getFormData(
    filter?: (item: IFieldStore | ITableFieldStore, fieldValue: IFieldValue) => boolean,
    config?: {
      includeHiddenField?: boolean;
      componentNameEnable?: boolean;
    },
  ): IFieldValue[];

  /**
   * Get form data as a map
   * @param filter - Optional filter function
   * @returns Field data map
   */
  getFormDataMap(
    filter?: (item: IFieldStore | ITableFieldStore, fieldValue: IFieldValue) => boolean,
  ): IFieldDataMap;

  /**
   * Get changed form data compared to initial values
   * @param filter - Optional filter function
   * @returns Promise resolving to array of changed field values
   */
  getChangedFormData(
    filter?: (item: IFieldStore | ITableFieldStore, fieldValue: IFieldValue) => boolean,
  ): Promise<IFieldValue[]>;

  /**
   * Get all form data asynchronously
   * @param filter - Optional filter function
   * @param config - Configuration options
   * @returns Promise resolving to array of field values
   */
  getAsyncAllFormData(
    filter?: (item: any, fieldValue: IFieldValue) => boolean,
    config?: {
      componentNameEnable?: boolean;
    },
  ): Promise<IFieldValue[]>;

  /**
   * Get form data asynchronously excluding empty values
   * @param filter - Optional filter function
   * @returns Promise resolving to array of field values
   */
  getAsyncFormData(
    filter?: (item: any, fieldValue: IFieldValue) => boolean,
  ): Promise<IFieldValue[]>;

  // ============================================================================
  // VALIDATION METHODS
  // ============================================================================

  /**
   * Validate all form data
   * @returns Validation result or Promise for async validation
   */
  validateFormData():
    | { valid: boolean; message: string }
    | Promise<{ valid: boolean; message: string }>;

  /**
   * @deprecated Use validateFormData instead
   * Check if form is valid
   * @returns Validation result
   */
  isFormValid(): { valid: boolean; message: string } | Promise<{ valid: boolean; message: string }>;

  /**
   * Set the currently invalid field
   * @param field - Field store that failed validation
   * @param forceClear - Whether to force clear existing invalid field
   */
  setInvalidField(field: IFieldStore | null, forceClear?: boolean): void;

  /**
   * Get the currently invalid field
   * @returns Invalid field store or null
   */
  getInvalidField(): IFieldStore | null;

  /**
   * Scroll to the invalid field
   * @param options - Scroll behavior options
   */
  scrollToInvalidField(options?: {
    block?: 'start' | 'center' | 'end' | 'nearest';
    inline?: 'start' | 'center' | 'end' | 'nearest';
    behavior?: 'auto' | 'smooth';
  }): void;

  /**
   * Hook called before form submission
   * @returns Promise resolving to validation result
   */
  beforeSubmit(): Promise<string | boolean>;

  // ============================================================================
  // EVENT METHODS
  // ============================================================================

  /**
   * Register initialization callback
   * @param fn - Callback function
   * @returns Unsubscribe function
   */
  onInit(fn: () => void): () => void;

  /**
   * Register form data change callback
   * @param fn - Callback function
   * @param config - Configuration options
   */
  onFormDataChange(
    fn: (value?: any, fieldStore?: IFieldStore) => void,
    config?: {
      debounce?: number;
    },
  ): void;

  // ============================================================================
  // COMPONENT METHODS
  // ============================================================================

  /**
   * Get React component by schema
   * @param item - Component schema
   * @returns React component class
   */
  getComponent(item: IComponentSchema): React.ComponentType | undefined;

  /**
   * Get global properties for component
   * @param componentName - Component name
   * @returns Global properties object
   */
  getGlobalProp(componentName: string): any;

  /**
   * Render a field component
   * @param fieldStore - Field store to render
   * @param index - Field index
   * @param renderMode - Render mode
   * @returns React element
   */
  renderComponent(
    fieldStore: IFieldStore | ITableFieldStore,
    index?: number,
    renderMode?: string,
  ): ReactElement;

  /**
   * Render the entire form
   * @returns React element
   */
  render(): ReactElement;
}

