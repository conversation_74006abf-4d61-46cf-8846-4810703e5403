/**
 * @fileoverview Form data type definitions based on sample data analysis
 * These types are derived from actual form data returned by window.__ctx.getFormData()
 */

// ============================================================================
// CORE FORM DATA TYPES
// ============================================================================

/**
 * Base field data structure returned by getFormData()
 */
export interface IFormFieldData {
  /** Field identifier/key */
  key: string;
  /** Field display label */
  label?: string;
  /** Field value - can be string, number, array, or object */
  value: any;
  /** Extended value with additional metadata */
  extendValue?: any;
}

/**
 * Employee/User information structure
 */
export interface IEmployeeInfo {
  /** Employee name */
  name: string;
  /** Employee ID */
  emplId: string;
  /** Avatar URL */
  avatar: string;
}

/**
 * Option metadata for select fields
 */
export interface ISelectOption {
  /** Option key/identifier */
  key: string;
  /** Option display label */
  label?: string;
}

/**
 * DingTalk origin department field data
 */
export interface IDingTalkOriginDeptData extends IFormFieldData {
  key: "dingtalk_origin_dept";
  value: string; // Employee ID
  extendValue: IEmployeeInfo;
}

/**
 * Text field data (single line input)
 */
export interface ITextFieldData extends IFormFieldData {
  value: string;
  extendValue?: undefined;
}

/**
 * Textarea field data (multi-line input)
 */
export interface ITextareaFieldData extends IFormFieldData {
  value: string;
  extendValue?: undefined;
}

/**
 * Number field data
 */
export interface INumberFieldData extends IFormFieldData {
  value: string | number;
  extendValue?: undefined;
}

/**
 * Single select field data
 */
export interface ISelectFieldData extends IFormFieldData {
  value: string;
  extendValue: ISelectOption;
}

/**
 * Multi-select field data
 */
export interface IMultiSelectFieldData extends IFormFieldData {
  value: string[];
  extendValue: ISelectOption[];
}

/**
 * Date field data
 */
export interface IDateFieldData extends IFormFieldData {
  value: string; // ISO date string or formatted date
  extendValue?: undefined;
}

/**
 * Date range field data
 */
export interface IDateRangeFieldData extends IFormFieldData {
  value: [string, string]; // Start and end dates
  extendValue?: {
    startDate: string;
    endDate: string;
  };
}

/**
 * Money field data
 */
export interface IMoneyFieldData extends IFormFieldData {
  value: string | number;
  extendValue?: {
    currency?: string;
    precision?: number;
  };
}

/**
 * Address field data
 */
export interface IAddressFieldData extends IFormFieldData {
  value: string;
  extendValue?: {
    province?: string;
    city?: string;
    district?: string;
    street?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
}

/**
 * Phone field data
 */
export interface IPhoneFieldData extends IFormFieldData {
  value: string;
  extendValue?: {
    countryCode?: string;
    areaCode?: string;
    number: string;
    type: 'mobile' | 'landline' | 'phone_tel';
  };
}

/**
 * ID Card field data
 */
export interface IIdCardFieldData extends IFormFieldData {
  value: string;
  extendValue?: {
    maskedValue?: string;
    isValid?: boolean;
  };
}

/**
 * Photo/Image field data
 */
export interface IPhotoFieldData extends IFormFieldData {
  value: string | string[]; // URL(s)
  extendValue?: {
    fileNames?: string[];
    fileSizes?: number[];
    thumbnails?: string[];
  };
}

/**
 * Attachment field data
 */
export interface IAttachmentFieldData extends IFormFieldData {
  value: string | string[]; // File URL(s)
  extendValue?: {
    fileNames: string[];
    fileSizes: number[];
    fileTypes: string[];
  };
}

/**
 * Contact field data (internal)
 */
export interface IInternalContactFieldData extends IFormFieldData {
  value: string | string[]; // Employee ID(s)
  extendValue: IEmployeeInfo | IEmployeeInfo[];
}

/**
 * Contact field data (external)
 */
export interface IExternalContactFieldData extends IFormFieldData {
  value: string | string[];
  extendValue?: {
    name: string;
    phone?: string;
    email?: string;
  } | Array<{
    name: string;
    phone?: string;
    email?: string;
  }>;
}

/**
 * Department field data
 */
export interface IDepartmentFieldData extends IFormFieldData {
  value: string | string[]; // Department ID(s)
  extendValue?: {
    name: string;
    id: string;
    parentId?: string;
  } | Array<{
    name: string;
    id: string;
    parentId?: string;
  }>;
}

/**
 * Star rating field data
 */
export interface IStarRatingFieldData extends IFormFieldData {
  value: number; // Rating value (e.g., 1-5)
  extendValue?: {
    maxRating?: number;
    description?: string;
  };
}

/**
 * Cascade field data
 */
export interface ICascadeFieldData extends IFormFieldData {
  value: string | string[];
  extendValue?: {
    path: string[];
    labels: string[];
  };
}

/**
 * Time and location field data
 */
export interface ITimeLocationFieldData extends IFormFieldData {
  value: {
    time: string;
    location: string;
  };
  extendValue?: {
    coordinates?: {
      latitude: number;
      longitude: number;
    };
    address?: string;
  };
}

/**
 * Table field data
 */
export interface ITableFieldData extends IFormFieldData {
  value: Array<Record<string, any>>;
  extendValue?: {
    columns: Array<{
      key: string;
      label: string;
      type: string;
    }>;
    totalRows?: number;
  };
}

/**
 * Text note field data (read-only display)
 */
export interface ITextNoteFieldData extends IFormFieldData {
  value: string;
  extendValue?: {
    html?: string;
    style?: Record<string, any>;
  };
}

// ============================================================================
// UNION TYPES
// ============================================================================

/**
 * Union type for all possible field data types
 */
export type IFormFieldDataUnion = 
  | IDingTalkOriginDeptData
  | ITextFieldData
  | ITextareaFieldData
  | INumberFieldData
  | ISelectFieldData
  | IMultiSelectFieldData
  | IDateFieldData
  | IDateRangeFieldData
  | IMoneyFieldData
  | IAddressFieldData
  | IPhoneFieldData
  | IIdCardFieldData
  | IPhotoFieldData
  | IAttachmentFieldData
  | IInternalContactFieldData
  | IExternalContactFieldData
  | IDepartmentFieldData
  | IStarRatingFieldData
  | ICascadeFieldData
  | ITimeLocationFieldData
  | ITableFieldData
  | ITextNoteFieldData;

// ============================================================================
// FORM DATA COLLECTION TYPES
// ============================================================================

/**
 * Complete form data array returned by getFormData()
 */
export type IFormDataArray = IFormFieldDataUnion[];

/**
 * Form data map with field keys as keys
 */
export type IFormDataMap = Record<string, IFormFieldDataUnion>;

/**
 * Form data validation result
 */
export interface IFormValidationResult {
  /** Whether the form is valid */
  isValid: boolean;
  /** Array of invalid field keys */
  invalidFields: string[];
  /** Validation error messages */
  errors: Record<string, string>;
}

/**
 * Form data export options
 */
export interface IFormDataExportOptions {
  /** Include empty fields */
  includeEmpty?: boolean;
  /** Include extended values */
  includeExtendValue?: boolean;
  /** Fields to exclude */
  excludeFields?: string[];
  /** Fields to include (if specified, only these fields will be included) */
  includeFields?: string[];
  /** Export format */
  format?: 'array' | 'map' | 'flat';
}

/**
 * Form data filter options
 */
export interface IFormDataFilterOptions {
  /** Field types to include */
  fieldTypes?: string[];
  /** Field keys to include */
  fieldKeys?: string[];
  /** Filter by required fields only */
  requiredOnly?: boolean;
  /** Filter by fields with values only */
  withValuesOnly?: boolean;
}

/**
 * Form data statistics
 */
export interface IFormDataStats {
  /** Total number of fields */
  totalFields: number;
  /** Number of filled fields */
  filledFields: number;
  /** Number of required fields */
  requiredFields: number;
  /** Number of invalid fields */
  invalidFields: number;
  /** Completion percentage */
  completionRate: number;
}