import { defineConfig } from '@rsbuild/core';
import { pluginReact } from '@rsbuild/plugin-react';

export default defineConfig({
  plugins: [pluginReact()],
  dev: {
    hmr: true,
    // Add source maps for better debugging
    ...(process.env.NODE_ENV === 'development' && {
      writeToDisk: false,
    }),
  },
  server: {
    host: '127.0.0.1',
    port: 3088,
  },
  moduleFederation: {
    options: {
      name: 'OAAIModules',
      filename: 'remoteEntry.js',
      exposes: {
        './AIFormFill': './src/entries/ai-form-fill.tsx',
      },
      remotes: {},
      // Add development specific config
      ...(process.env.NODE_ENV === 'development' && {
        library: { type: 'var', name: 'OAAIModules' },
      }),
    },
  },
  tools: {
    rspack: {
      externals: process.env.NODE_ENV === 'production' ? {
        react: 'React',
        'react-dom': 'ReactDOM',
      } : {},
      devtool: process.env.NODE_ENV === 'development' ? 'eval-cheap-module-source-map' : false,
      optimization: process.env.NODE_ENV === 'development' ? {
        // Simplified optimization for development with module federation
        splitChunks: false, // Disable chunk splitting in development
      } : {
        // Production optimization
        splitChunks: {
          chunks: 'all',
          minSize: 204800, // 200KB minimum chunk size
          maxSize: 0, // No maximum size limit
          minChunks: 1,
          cacheGroups: {
            default: {
              minChunks: 2,
              priority: -20,
              reuseExistingChunk: true,
              minSize: 204800,
            },
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              priority: -10,
              minSize: 204800,
            },
          },
        },
      },
    },
  },
  output: {
    distPath: {
      root: 'dist',
    },
    filename: {
      html: 'index.html',
    },
    // injectStyles: true,
    // inlineScripts: true,
    legalComments: 'none',
    target: 'web',
    assetPrefix: 'https://dev.g.alicdn.com/SWH5/sw-ai-federated-modules/0.0.1/',
  },
  source: {
    alias: {
      '@': './src',
    },
  },
});
